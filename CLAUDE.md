# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Commands
- `pnpm dev` - Start development server
- `pnpm build` - Build for production (includes type checking)
- `pnpm preview` - Preview production build
- `pnpm generate:api` - Generate API code from backend metadata

### Manual Commands (inferred from codebase)
- `vue-tsc -b` - Type check only
- `pnpm install` - Install dependencies

## Project Architecture

### Tech Stack
- **Framework**: Vue 3 with Composition API and TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS v4 with utilities
- **UI Components**: Reka UI (Shadcn Vue style)
- **State Management**: Pinia
- **API Client**: Alova with Vue integration
- **Package Manager**: pnpm

### Directory Structure
```
src/
├── api/             # API层和请求配置
├── components/      # Vue组件
│   ├── ui/         # 基础UI组件
│   └── shared/     # 通用组件
├── composables/    # Vue组合式函数
├── constants/      # 项目常量
├── lib/           # 工具函数
├── router/        # 路由配置
├── stores/        # Pinia状态管理
├── types/         # TypeScript类型定义
└── views/         # 页面组件
```

### Key Architecture Patterns

**API Layer**:
- Alova instance configured with interceptors in `src/api/client.ts`
- Unified response format with `ResponseModel<T>` and `ResponseListModel<T>`
- Automatic auth token injection and error handling
- Business logic error codes with Chinese error messages

**Type System**:
- Strict TypeScript with `ModelBase` interface for all models
- Comprehensive filter and query parameter types (`FilterOperator`, `QueryParams`)
- Enum-based filtering with operators like `eq`, `ne`, `ilike`, etc.

**Code Generation**:
- Custom API code generator at `scripts/generate-api.js`
- Generates TypeScript interfaces from backend metadata
- Supports enum extraction from backend `get_metadata` endpoints
- Creates complete CRUD operations with proper typing

### Import Aliases
- `@/` → `src/` (configured in both Vite and TypeScript)

### API Patterns
- RESTful endpoints following pattern: `/{module}/query`, `/{module}/get/{id}`, etc.
- Pagination with `offset`/`limit` parameters
- Complex filtering with nested conditions and operators
- Automatic token-based authentication

### Code Generation Workflow
1. Use `pnpm generate:api` to start interactive generator
2. Provide module name (e.g., `sys/user`) and model name (e.g., `User`)
3. Generator fetches metadata from backend `/get_metadata` endpoint
4. Creates TypeScript interfaces and API functions with proper typing
5. Supports enum extraction from backend metadata

## Development Guidelines

### Component Development
- Use `<script setup lang="ts">` syntax
- Define Props and Emits interfaces explicitly
- Prefer composition API patterns
- Use `withDefaults()` for default props

### API Integration
- Use Alova methods: `Get`, `Post`, `Put`, `Delete`
- Follow the established response patterns
- Handle errors through the configured interceptors
- Use proper TypeScript typing for all API calls

### Styling
- Use Tailwind CSS utilities primarily
- Follow utility-first approach
- Use `cn()` function for conditional classes
- Responsive design with Tailwind breakpoints