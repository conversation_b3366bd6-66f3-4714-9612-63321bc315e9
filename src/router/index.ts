import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/LoginPage.vue'),
    meta: {
      requiresAuth: false,
      title: '登录'
    }
  },
  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundPage.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 玻璃生产管理系统`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    // 如果没有登录，跳转到登录页
    if (!authStore.isAuthenticated) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 检查 token 是否需要刷新
    try {
      await authStore.checkAndRefreshToken()
    } catch (error) {
      console.error('Token refresh failed:', error)
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  } else {
    // 如果已经登录，访问登录页时跳转到首页
    if (to.path === '/login' && authStore.isAuthenticated) {
      next('/')
      return
    }
  }

  next()
})

export default router