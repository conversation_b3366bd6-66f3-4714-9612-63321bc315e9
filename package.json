{"name": "mes_prd", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "type:check": "vue-tsc --noEmit", "generate:api": "node scripts/generate-api.js", "test:api": "node scripts/test-generated-api.js"}, "dependencies": {"@alova/vue-options": "^2.0.13", "@tailwindcss/vite": "^4.1.11", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.5.0", "alova": "^3.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.0", "embla-carousel-vue": "^8.6.0", "lucide-vue-next": "^0.525.0", "pinia": "^3.0.3", "reka-ui": "^2.3.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "vaul-vue": "^0.4.1", "vee-validate": "^4.15.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "vue-sonner": "^2.0.2", "zod": "^4.0.5"}, "devDependencies": {"@types/node": "^24.0.13", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}